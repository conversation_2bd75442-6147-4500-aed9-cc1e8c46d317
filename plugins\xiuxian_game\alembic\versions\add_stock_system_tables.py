"""添加股票系统表

Revision ID: add_stock_system_tables
Revises: 019b6d7cd48e
Create Date: 2025-07-31 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_stock_system_tables'
down_revision: Union[str, None] = '019b6d7cd48e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 创建股票表
    op.create_table('stocks',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('symbol', sa.String(length=10), nullable=False, comment='股票代码'),
        sa.Column('name', sa.String(length=50), nullable=False, comment='股票名称'),
        sa.Column('stock_type', sa.String(length=20), nullable=False, comment='股票类型'),
        sa.Column('base_price', sa.Integer(), nullable=False, comment='基础价格（金币）'),
        sa.Column('current_price', sa.Integer(), nullable=False, comment='当前价格（金币）'),
        sa.Column('total_shares', sa.BigInteger(), nullable=False, server_default='1000000', comment='总股本'),
        sa.Column('circulating_shares', sa.BigInteger(), nullable=False, server_default='1000000', comment='流通股本'),
        sa.Column('volatility', sa.Float(), nullable=False, server_default='0.05', comment='波动率（0.01-0.20）'),
        sa.Column('trend_factor', sa.Float(), nullable=False, server_default='1.0', comment='趋势因子（0.8-1.2）'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='1', comment='是否可交易'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('symbol')
    )
    op.create_index('ix_stocks_symbol', 'stocks', ['symbol'])
    
    # 创建股票持仓表
    op.create_table('stock_holdings',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('player_id', sa.String(length=32), nullable=False),
        sa.Column('stock_id', sa.Integer(), nullable=False),
        sa.Column('shares', sa.Integer(), nullable=False, server_default='0', comment='持有股数'),
        sa.Column('avg_cost', sa.Integer(), nullable=False, server_default='0', comment='平均成本价（金币）'),
        sa.Column('total_cost', sa.BigInteger(), nullable=False, server_default='0', comment='总成本（金币）'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
        sa.ForeignKeyConstraint(['player_id'], ['player.id'], ),
        sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_stock_holdings_player_id', 'stock_holdings', ['player_id'])
    op.create_index('ix_stock_holdings_stock_id', 'stock_holdings', ['stock_id'])
    op.create_index('idx_player_stock', 'stock_holdings', ['player_id', 'stock_id'], unique=True)
    
    # 创建股票交易记录表
    op.create_table('stock_transactions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('player_id', sa.String(length=32), nullable=False),
        sa.Column('stock_id', sa.Integer(), nullable=False),
        sa.Column('transaction_type', sa.String(length=10), nullable=False, comment='交易类型'),
        sa.Column('shares', sa.Integer(), nullable=False, comment='交易股数'),
        sa.Column('price', sa.Integer(), nullable=False, comment='交易价格（金币/股）'),
        sa.Column('total_amount', sa.BigInteger(), nullable=False, comment='交易总金额（金币）'),
        sa.Column('commission', sa.Integer(), nullable=False, server_default='0', comment='手续费（金币）'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
        sa.ForeignKeyConstraint(['player_id'], ['player.id'], ),
        sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_stock_transactions_player_id', 'stock_transactions', ['player_id'])
    op.create_index('ix_stock_transactions_stock_id', 'stock_transactions', ['stock_id'])
    
    # 创建股票价格历史表
    op.create_table('stock_price_history',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('stock_id', sa.Integer(), nullable=False),
        sa.Column('price', sa.Integer(), nullable=False, comment='价格（金币）'),
        sa.Column('change_amount', sa.Integer(), nullable=False, server_default='0', comment='变动金额'),
        sa.Column('change_rate', sa.Float(), nullable=False, server_default='0.0', comment='变动比例'),
        sa.Column('volume', sa.Integer(), nullable=False, server_default='0', comment='成交量'),
        sa.Column('recorded_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
        sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_stock_price_history_stock_id', 'stock_price_history', ['stock_id'])
    op.create_index('ix_stock_price_history_recorded_at', 'stock_price_history', ['recorded_at'])
    op.create_index('idx_stock_time', 'stock_price_history', ['stock_id', 'recorded_at'])
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_stock_time', table_name='stock_price_history')
    op.drop_index('ix_stock_price_history_recorded_at', table_name='stock_price_history')
    op.drop_index('ix_stock_price_history_stock_id', table_name='stock_price_history')
    op.drop_table('stock_price_history')
    
    op.drop_index('ix_stock_transactions_stock_id', table_name='stock_transactions')
    op.drop_index('ix_stock_transactions_player_id', table_name='stock_transactions')
    op.drop_table('stock_transactions')
    
    op.drop_index('idx_player_stock', table_name='stock_holdings')
    op.drop_index('ix_stock_holdings_stock_id', table_name='stock_holdings')
    op.drop_index('ix_stock_holdings_player_id', table_name='stock_holdings')
    op.drop_table('stock_holdings')
    
    op.drop_index('ix_stocks_symbol', table_name='stocks')
    op.drop_table('stocks')
    # ### end Alembic commands ###
